  .main{
    flex: 1;
    min-height: 100vh;
    padding-bottom: 15vh;
    position: relative;
  }
  .main .nav{
    display: flex;
    align-items: center;
    justify-content: space-between;
    font-size: 22px;
    padding: 20px;
    color: #585858 ;
  }
  .main .nav img{
    image-rendering: auto;
    width: 40px;
    height: 40px;
    object-fit:cover;
    border-radius: 50%;
  }

  .main-container{
    max-width: 900px;
    margin: auto;
  }
 
  .main .greet{
    margin: 50px 0px;
    font-size: 56px;
    color: #c4c7c5;
    font-weight: 500;
    padding: 20px;
  }
  .main .greet span{
    background: -webkit-linear-gradient(16deg,#4b90ff,#ff5546);
    background-clip: text;
    -webkit-text-fill-color: transparent;
  }
  .main .cards{
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(180px,1fr));
    gap: 15px;
    padding: 20px;
  }
  .main .card{
    height: 200px;
    padding: 15px;
    background-color: #f0f4f9;
    border-radius: 10px;
    position: relative;
    cursor: pointer;
  }

  .main .card img{
    width: 35px;
    padding: 5px;
    position: absolute;
    background-color: white;
    border-radius: 20px;
    bottom: 10px;
    right: 10px;
  }
  .main .card p{
    color: #585858;
    font-size: 17px;
  }
  .main .card:hover{
    background-color: #dfe4ea;
  }
  .main-bottom{
    position: absolute;
    max-width: 900px;
    padding: 0px 20px;
    margin: auto;
  }
  .search-box{
    width: 860px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: 20px;
    background-color: #f0f4f9;
    padding: 10px 20px;
    border-radius: 50px;
  }
  .search-box img{
    width: 24px;
  }
  .search-box input{
    flex: 1;
    background: transparent;
    border: none;
    outline: none;
    padding: 8px;
    font-size: 18px;

  }
  .search-box div{
    display: flex;
    align-items: center;
    gap: 15px;

  }
  .main .bottom-info{
    font-size: 13px;
    margin: 15px auto;
    text-align: center;
    font-weight: 300;

  }
