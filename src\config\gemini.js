//const apiKey ="AIzaSyA1CNO9ulTTZMHdZgse5XWWjciSOaELwlU"

// To run this code you need to install the following dependencies:
// npm install @google/genai mime
// npm install -D @types/node

import { GoogleGenAI } from "@google/genai";

async function main(prompt) {
  const ai = new GoogleGenAI({
    apiKey: "AIzaSyDzxiDaRLgU2ZVngA35Ecoij-hpH9SuJUI",
  });

  const tools = [
    {
      googleSearch: {}, // Optional, depending on use case
    },
  ];

  const config = {
    thinkingConfig: {
      thinkingBudget: -1,
    },
    tools,
    responseMimeType: "text/plain",
  };

  const model = "gemini-2.5-pro";

  const contents = [
    {
      role: "user",
      parts: [
        {
          text: `prompt`,
        },
      ],
    },
  ];

  const response = await ai.models.generateContentStream({
    model,
    config,
    contents,
  });

  for await (const chunk of response) {
    console.log(chunk.text);
  }
}

export default main;
